<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.9" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Component class="javax.swing.JButton" name="jButton1">
      <Properties>
        <Property name="text" type="java.lang.String" value="jButton1"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="jButton2">
      <Properties>
        <Property name="text" type="java.lang.String" value="jButton2"/>
      </Properties>
    </Component>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="title" type="java.lang.String" value="TOTP"/>
    <Property name="minimumSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
      <Dimension value="[600, 600]"/>
    </Property>
    <Property name="name" type="java.lang.String" value="TOTP" noResource="true"/>
    <Property name="resizable" type="boolean" value="false"/>
    <Property name="size" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
      <Dimension value="[580, 570]"/>
    </Property>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
    <AuxValue name="designerSize" type="java.awt.Dimension" value="-84,-19,0,5,115,114,0,18,106,97,118,97,46,97,119,116,46,68,105,109,101,110,115,105,111,110,65,-114,-39,-41,-84,95,68,20,2,0,2,73,0,6,104,101,105,103,104,116,73,0,5,119,105,100,116,104,120,112,0,0,2,62,0,0,2,77"/>
  </AuxValues>

  <Layout class="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout">
    <Property name="useNullLayout" type="boolean" value="false"/>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="lb_title">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Helvetica Neue" size="36" style="1"/>
        </Property>
        <Property name="horizontalAlignment" type="int" value="2"/>
        <Property name="text" type="java.lang.String" value="TOTP"/>
        <Property name="horizontalTextPosition" type="int" value="0"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="8" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_username">
      <Properties>
        <Property name="text" type="java.lang.String" value="Username:"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="61" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_username">
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="83" width="200" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_password">
      <Properties>
        <Property name="text" type="java.lang.String" value="Password:"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="111" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JPasswordField" name="txt_password">
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="133" width="200" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JButton" name="btn_login">
      <Properties>
        <Property name="text" type="java.lang.String" value="Login"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_loginActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="150" y="160" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JButton" name="btn_register">
      <Properties>
        <Property name="text" type="java.lang.String" value="Register"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_registerActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="161" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_icon">
      <Properties>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[60, 60]"/>
        </Property>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="410" y="110" width="40" height="40"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_qrcode">
      <Properties>
        <Property name="horizontalAlignment" type="int" value="0"/>
        <Property name="text" type="java.lang.String" value="QRCode Is HERE!!!"/>
        <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
          <Border info="org.netbeans.modules.form.compat2.border.LineBorderInfo">
            <LineBorder/>
          </Border>
        </Property>
        <Property name="horizontalTextPosition" type="int" value="0"/>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[200, 200]"/>
        </Property>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="330" y="30" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_checktoken">
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="208" width="124" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_checkOTP">
      <Properties>
        <Property name="text" type="java.lang.String" value="Check Token TOTP"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="190" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_tokenStatus">
      <Properties>
        <Property name="horizontalAlignment" type="int" value="0"/>
        <Property name="text" type="java.lang.String" value="Token Status"/>
        <Property name="horizontalTextPosition" type="int" value="0"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="230" y="200" width="40" height="40"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_otpToken">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Helvetica Neue" size="48" style="1"/>
        </Property>
        <Property name="horizontalAlignment" type="int" value="0"/>
        <Property name="text" type="java.lang.String" value="######"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="350" y="270" width="190" height="78"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JButton" name="btn_copy">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="10" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Copy"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_copyActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="470" y="250" width="70" height="20"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_OTPtoken">
      <Properties>
        <Property name="text" type="java.lang.String" value="OTP Token"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="350" y="250" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="355" width="520" height="120"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="tb_accountpool">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="6" rowCount="0">
                <Column editable="false" title="Username" type="java.lang.String"/>
                <Column editable="false" title="Hash" type="java.lang.String"/>
                <Column editable="false" title="Secret Key" type="java.lang.String"/>
                <Column editable="false" title="Token" type="java.lang.String"/>
                <Column editable="false" title="S" type="java.lang.String"/>
                <Column editable="true" title="" type="java.lang.Boolean"/>
              </Table>
            </Property>
            <Property name="cellSelectionEnabled" type="boolean" value="true"/>
            <Property name="columnModel" type="javax.swing.table.TableColumnModel" editor="org.netbeans.modules.form.editors2.TableColumnModelEditor">
              <TableColumnModel selectionModel="1">
                <Column maxWidth="200" minWidth="75" prefWidth="75" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="200" minWidth="75" prefWidth="75" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="60" minWidth="60" prefWidth="60" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="32" minWidth="32" prefWidth="32" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="25" minWidth="25" prefWidth="25" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
              </TableColumnModel>
            </Property>
            <Property name="selectionMode" type="int" value="0"/>
            <Property name="tableHeader" type="javax.swing.table.JTableHeader" editor="org.netbeans.modules.form.editors2.JTableHeaderEditor">
              <TableHeader reorderingAllowed="true" resizingAllowed="true"/>
            </Property>
          </Properties>
          <Events>
            <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="tb_accountpoolMouseClicked"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JScrollPane" name="jScrollPane2">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="271" width="300" height="78"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="txt_otpauth">
          <Properties>
            <Property name="columns" type="int" value="20"/>
            <Property name="lineWrap" type="boolean" value="true"/>
            <Property name="rows" type="int" value="5"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="lb_otpauth">
      <Properties>
        <Property name="text" type="java.lang.String" value="OTPAuth"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="250" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_timer">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="420" y="250" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JButton" name="btn_checktoken">
      <Properties>
        <Property name="text" type="java.lang.String" value="Check"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_checktokenActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="150" y="208" width="-1" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JButton" name="btn_openFile">
      <Properties>
        <Property name="text" type="java.lang.String" value="Open"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_openFileActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="470" y="480" width="70" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JButton" name="btn_saveFile">
      <Properties>
        <Property name="text" type="java.lang.String" value="Save"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_saveFileActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="470" y="510" width="70" height="-1"/>
        </Constraint>
      </Constraints>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane3">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="20" y="480" width="440" height="60"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="txt_directory">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
            <Property name="columns" type="int" value="20"/>
            <Property name="lineWrap" type="boolean" value="true"/>
            <Property name="rows" type="int" value="5"/>
            <Property name="maximumSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
              <Dimension value="[200, 30]"/>
            </Property>
            <Property name="minimumSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
              <Dimension value="[100, 20]"/>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JButton" name="btn_checkall">
      <Properties>
        <Property name="toolTipText" type="java.lang.String" value="CheckAll"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_checkallActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="550" y="360" width="30" height="30"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JButton" name="btn_delete">
      <Properties>
        <Property name="toolTipText" type="java.lang.String" value="Delete"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_deleteActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="550" y="400" width="30" height="30"/>
        </Constraint>
      </Constraints>
    </Component>
    <Component class="javax.swing.JButton" name="btn_clean">
      <Properties>
        <Property name="toolTipText" type="java.lang.String" value="Clean"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_cleanActionPerformed"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout" value="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout$AbsoluteConstraintsDescription">
          <AbsoluteConstraints x="550" y="440" width="30" height="30"/>
        </Constraint>
      </Constraints>
    </Component>
  </SubComponents>
</Form>
