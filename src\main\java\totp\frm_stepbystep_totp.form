<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="2"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <Events>
    <EventHandler event="windowClosed" listener="java.awt.event.WindowListener" parameters="java.awt.event.WindowEvent" handler="formWindowClosed"/>
  </Events>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="1" attributes="0">
                  <Group type="102" attributes="0">
                      <Component id="lb_formular" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" max="-2" attributes="0">
                          <Group type="102" attributes="0">
                              <Component id="lb_formular_1" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="32767" attributes="0"/>
                              <Component id="txt_token" min="-2" pref="158" max="-2" attributes="0"/>
                          </Group>
                          <Component id="lb_Title" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="lb_Timestep" min="-2" max="-2" attributes="0"/>
                              <EmptySpace type="unrelated" max="-2" attributes="0"/>
                              <Component id="lb_time" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="lb_second" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="lb_timeStamp" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="1" attributes="0">
                              <Component id="lb_secretkey" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_secretkey" max="32767" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="txt_byte_1" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_2" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_3" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_4" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_5" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_6" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_7" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_8" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_9" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_10" min="-2" pref="35" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="txt_token_byte" min="-2" pref="158" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="lb_mod" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="txt_byte_21" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_22" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_23" min="-2" pref="35" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="txt_byte_24" min="-2" pref="35" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="lb_offset" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="lb_offset_byte" min="-2" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                  </Group>
                  <Group type="102" alignment="1" attributes="0">
                      <Component id="txt_byte_11" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_12" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_13" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_14" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_15" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_16" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_17" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_18" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_19" min="-2" pref="35" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="txt_byte_20" min="-2" pref="35" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace pref="15" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="lb_Title" min="-2" max="-2" attributes="0"/>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="lb_Timestep" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="lb_time" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel1" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="lb_second" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="lb_timeStamp" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="lb_secretkey" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_secretkey" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txt_byte_1" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_5" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_4" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_8" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_7" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_10" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_3" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_6" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_9" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txt_byte_12" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_13" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_14" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_15" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_16" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_17" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_18" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_19" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_20" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_byte_11" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="12" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="lb_offset_byte" min="-2" max="-2" attributes="0"/>
                  <Component id="lb_offset" alignment="0" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" max="-2" attributes="0">
                  <Group type="102" attributes="0">
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="txt_byte_24" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_byte_22" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_byte_21" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="txt_byte_23" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="lb_formular_1" min="-2" pref="17" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="txt_token_byte" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="lb_mod" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="lb_formular" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                  </Group>
                  <Component id="txt_token" max="32767" attributes="0"/>
              </Group>
              <EmptySpace pref="27" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="lb_Title">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Helvetica Neue" size="18" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="STEP BY STEP - TOTP"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_Timestep">
      <Properties>
        <Property name="text" type="java.lang.String" value="Time Step:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_time">
      <Properties>
        <Property name="text" type="java.lang.String" value="TIME"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_secretkey">
      <Properties>
        <Property name="text" type="java.lang.String" value="Secret Key:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_secretkey">
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_1">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_2">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_3">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_4">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_5">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_6">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_7">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_8">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_9">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_10">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_11">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_12">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_13">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_14">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_15">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_16">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_17">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_18">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_19">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_20">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_offset">
      <Properties>
        <Property name="text" type="java.lang.String" value="Offset:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_21">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_22">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_23">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_byte_24">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_offset_byte">
      <Properties>
        <Property name="text" type="java.lang.String" value="XX"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_formular_1">
      <Properties>
        <Property name="text" type="java.lang.String" value="+ 0x 7FFFFFFF"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_token_byte">
      <Properties>
        <Property name="editable" type="boolean" value="false"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_mod">
      <Properties>
        <Property name="text" type="java.lang.String" value=") Mod 10^6"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_formular">
      <Properties>
        <Property name="text" type="java.lang.String" value="("/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_token">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Helvetica Neue" size="42" style="1"/>
        </Property>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="text" type="java.lang.String" value="|"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_second">
      <Properties>
        <Property name="text" type="java.lang.String" value="Second"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="|"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lb_timeStamp">
      <Properties>
        <Property name="text" type="java.lang.String" value="TimeStamp"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
